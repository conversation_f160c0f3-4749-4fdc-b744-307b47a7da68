import { IActionProcess, IActionProcessItem, IWorkflowNode } from '@/types';
import { IChunkChatCompletionResponse } from '@/types/event';

export interface ParsedData {
  id?: string;
  task_id?: string;
  position?: number;
  tool?: string;
  tool_input?: string;
  observation?: string;
  message_files?: string[];
  event?: IChunkChatCompletionResponse['event'];
  answer?: string;
  conversation_id: string;
  message_id?: string;
  intention_id?: string;

  // 类型
  type?: 'image';
  // 图片链接
  url?: string;

  data: {
    // 工作流节点的数据
    id: string;
    node_type?: IWorkflowNode['type'];
    title?: string;
    inputs: string;
    outputs: string;
    action_process?: IActionProcessItem;
    action_type?: IActionProcess['action_type'];
    process_data: string;
    elapsed_time: number;
    execution_metadata?: IWorkflowNode['execution_metadata'];
    duration?: number;
  };
}

export interface InputData {
  message: {
    session_id: string;
    message_id: number;
    intention_id: number;
    event_time: number;
    content: string;
    description: string;
    id: string;
    duration: number;
    action_process: IActionProcessItem;
    action_type: IActionProcess['action_type'];
  };
  event_type: IChunkChatCompletionResponse['event'];
}

export const transform = (input: InputData): ParsedData => {
  const { message, event_type } = input;

  return {
    conversation_id: message.session_id.toString(),
    message_id: message.message_id.toString(),
    data: {
      id: message.id,
      node_type: 'question-classifier',
      title: message.description,
      inputs: '',
      outputs: message.content,
      process_data: '',
      elapsed_time: message.duration,
      action_process: message.action_process,
      action_type: message.action_type,

      // execution_metadata: {
      //   total_tokens: 120,
      //   total_price: 0.0006,
      //   currency: 'USD',
      // },
    },
    answer: message.content,
    tool: message.content,
    tool_input: message.content,
    observation: message.content,
    event: event_type,
  };
};
