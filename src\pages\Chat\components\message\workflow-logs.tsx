import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  InfoOutlined,
  LoadingOutlined,
} from '@ant-design/icons';
import { Collapse } from 'antd';
import { useState } from 'react';

import { IAgentMessage, IWorkflowNode } from '@/types';
import { MarkdownRenderer } from '../markdown-renderer';
import Thought<PERSON>hain from '../thought-chain';
import styles from './content.module.css';
import WorkflowNodeIcon from './workflow-node-icon';

interface IWorkflowLogsProps {
  status: NonNullable<IAgentMessage['workflows']>['status'];
  items: IWorkflowNode[];
  animate?: boolean;
  /**
   * 是否为历史消息
   */
  isHistory?: boolean;
}

export default function WorkflowLogs(props: IWorkflowLogsProps) {
  const { items, status, animate = true, isHistory } = props;

  // 管理外层 Collapse 的展开状态
  const [outerActiveKey, setOuterActiveKey] = useState<string[]>(['workflow']);

  // 管理内层 Collapse 的展开状态
  const [innerActiveKey, setInnerActiveKey] = useState<string[]>([]);

  // 当 items 变化时，更新内层 Collapse 的展开状态
  const { useEffect } = require('react');
  useEffect(() => {
    const keyList: string[] = animate || !isHistory ? items.map((item) => item.id) : [];
    setInnerActiveKey(keyList);
  }, [items, animate, isHistory]);

  // 只 append 新节点动画
  // const [renderedItems, setRenderedItems] = React.useState<any[]>([]);
  // React.useEffect(() => {
  //   if (!animate) {
  //     setRenderedItems(items);
  //     return;
  //   }
  //   if (items.length > renderedItems.length) {
  //     setTimeout(() => {
  //       setRenderedItems(items.slice(0, renderedItems.length + 1));
  //     }, 100);
  //   }
  // }, [items, animate, renderedItems.length]);

  if (!items?.length) {
    return null;
  }

  // const visibleItems = renderedItems;

  const keyList: string[] = animate || !isHistory ? items.map((item) => item.id) : [];
  console.log('keyList:', keyList);

  const collapseItems = [
    {
      key: 'workflow',
      label: (
        <div className="flex items-center justify-between">
          <div>计划</div>
          {status === 'running' ? (
            <LoadingOutlined />
          ) : status === 'finished' ? (
            <div className="text-green-700 flex items-center">
              <span className="mr-2">成功</span>
              <CheckCircleOutlined className="text-green-700" />
            </div>
          ) : null}
        </div>
      ),
      children: (
        <Collapse
          size="small"
          defaultActiveKey={keyList}
          items={items.map((item, index) => {
            const totalTokens = item.execution_metadata?.total_tokens;
            return {
              key: item.id,
              label: (
                <div className={animate && index === items.length - 1 ? styles['fade-in'] : ''}>
                  <div className="flex items-center justify-between w-full">
                    <div className="flex items-center">
                      <div className="mr-2">
                        <WorkflowNodeIcon type={item.type} />
                      </div>
                      <div>{item.title}</div>
                    </div>
                    <div className="flex items-center">
                      {item.status === 'success' ? (
                        <>
                          <div>{item.elapsed_time} 秒</div>
                          <div className="ml-3">{totalTokens ? `${totalTokens} tokens` : ''}</div>
                        </>
                      ) : null}
                      {item.status === 'success' ? (
                        <CheckCircleOutlined className="text-green-700" />
                      ) : item.status === 'error' ? (
                        <CloseCircleOutlined className="text-red-700" />
                      ) : item.status === 'running' ? (
                        <LoadingOutlined />
                      ) : (
                        <InfoOutlined />
                      )}
                    </div>
                  </div>
                </div>
              ),
              children: (
                <>
                  {item.action_process?.action_type === 'tools' ? (
                    <>
                      <ThoughtChain
                        key={`thought-${index}`}
                        uniqueKey={`${item.id}-thought-${index}`}
                        items={item.action_process?.action_process}
                        className="mt-3"
                        animate={animate}
                      />
                      <MarkdownRenderer markdownText={item.outputs || ''} />
                    </>
                  ) : (
                    <>
                      <MarkdownRenderer
                        markdownText={`${
                          item?.action_process?.action_process
                            ? item?.action_process?.action_process
                            : ''
                        }`}
                      />
                      <MarkdownRenderer markdownText={item?.outputs || ''} />
                    </>
                  )}
                </>
                // <Collapse
                //   size="small"
                //   items={[
                //     {
                //       key: `${item.id}-input`,
                //       label: '输入',
                //       children: <WorkflowNodeDetail originalContent={item.inputs} />,
                //     },
                //     {
                //       key: `${item.id}-process`,
                //       label: '处理过程',
                //       children: <WorkflowNodeDetail originalContent={item.process_data} />,
                //     },
                //     {
                //       key: `${item.id}-output`,
                //       label: '输出',
                //       children: <WorkflowNodeDetail originalContent={item.outputs} />,
                //     },
                //   ]}
                // ></Collapse>
              ),
            };
          })}
        >
          {}
        </Collapse>
      ),
    },
  ];

  return (
    <div className="min-w-chat-card my-3">
      <Collapse
        items={collapseItems}
        size="small"
        className="bg-white"
        defaultActiveKey={['workflow']}
      />
    </div>
  );
}
