import { useXAgent, useXChat, XStream } from '@ant-design/x';
import { useState } from 'react';

import { EventEnum } from '@/enums';
import { IGetAppParametersResponse } from '@/pages/Chat/type';
import chat from '@/services/chat';
import { ChatAPI } from '@/services/chat/typeings';
import { IAgentMessage, IMessageFileItem, IRequestInput, IWorkflowNode } from '@/types';
import { IAgentThought, IErrorEvent } from '@/types/event';
import { isTempId } from '@/utils';
import { ParsedData, transform } from '@/utils/transform';
import { useModel } from '@umijs/max';

export const useX = (options: {
  latestProps: React.MutableRefObject<{
    conversationId: string | undefined;
  }>;
  latestState: React.MutableRefObject<{
    inputParams: {
      [key: string]: unknown;
    };
  }>;
  appParameters?: IGetAppParametersResponse;
  filesRef: React.MutableRefObject<any[]>;
  abortRef: React.MutableRefObject<() => void>;
  getConversationMessages: (conversationId: string) => void;
  onConversationIdChange: (id: string) => void;
  conversationItemsChangeCallback: () => void;
}) => {
  const {
    latestProps,
    // latestState,
    filesRef,
    abortRef,
    getConversationMessages,
    onConversationIdChange,
    conversationItemsChangeCallback,
  } = options;
  const [currentMessageId, setCurrentMessageId] = useState<string>('');
  // const [messageLoading, setMesageLoading] = useState<boolean>(false);
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  const [abortController] = useState(() => new AbortController());

  const [agent] = useXAgent<IAgentMessage, { message: IRequestInput }, IAgentMessage>({
    request: async ({ message }, { onSuccess, onUpdate, onError }) => {
      // 发送消息
      try {
        onUpdate({
          requestStatus: 'loading',
          content: '',
          contentBlocks: [],
        });

        let params: ChatAPI.SendMessageReq = {
          user_id: currentUser?.user_id,
          prompt: message?.content,
          files: filesRef.current || [],
        };
        if (!isTempId(latestProps.current.conversationId)) {
          params.session_id = latestProps.current.conversationId;
        }

        const response = await chat.api.sendMessage(params, {
          signal: abortController.signal,
        });
        onUpdate({
          requestStatus: 'success',
          content: '',
          contentBlocks: [],
        });

        let result = '';
        const contentChunks: string[] = [];
        const files: IMessageFileItem[] = [];
        const workflows: IAgentMessage['workflows'] = {};
        const agentThoughts: IAgentThought[] = [];
        const contentBlocks: any = [];

        const readableStream = XStream({
          readableStream: response.body as NonNullable<ReadableStream>,
        });

        const reader = readableStream.getReader();
        abortRef.current = () => {
          console.log('abortController.current');
          abortController.abort?.();
          // reader?.cancel();
          reader.releaseLock();
          // onError({
          //   name: 'abort',
          //   message: '用户已取消',
          // });
        };
        console.log('reader=========reader', reader);

        while (reader) {
          const { value: chunk, done } = await reader.read();
          if (done) {
            reader.releaseLock(); // 释放读取器锁
            onSuccess([
              {
                content: result,
                contentBlocks,
              },
            ]);
            break;
          }
          if (!chunk) continue;
          if (chunk.data) {
            let parsedData = {} as ParsedData;
            // 获取最后一个内容块
            const lastItem = contentBlocks[contentBlocks.length - 1];
            try {
              parsedData = transform(JSON.parse(chunk.data));
              console.log('reader=========chunk', chunk);
              console.log('reader=========parsedData', parsedData);
            } catch (error) {
              console.error('解析 JSON 失败', error);
            }
            setCurrentMessageId(parsedData?.intention_id || '');
            if (parsedData.event === EventEnum.MESSAGE_END) {
              console.log('消息结束', parsedData);

              // const item: any = {
              //   type: EventEnum.MESSAGE_END,
              //   content: result,
              //   files,
              //   agentThoughts,
              //   workflows: {
              //     status: 'running',
              //     nodes: [],
              //   },
              // };
              // contentBlocks.push(item);
              onSuccess([
                {
                  requestStatus: 'success',
                  content: result,
                  message_id: parsedData.intention_id,
                  contentBlocks,
                },
              ]);
              // 刷新消息列表
              // getConversationMessages(parsedData.conversation_id);
              // onConversationIdChange(parsedData.conversation_id);
              if (parsedData.conversation_id && isTempId(latestProps.current.conversationId)) {
                console.log('parsedData.conversation_id', parsedData.conversation_id);

                // 通知外部组件，对话 ID 变更，外部组件需要更新对话列表
                onConversationIdChange(String(parsedData.conversation_id));
                conversationItemsChangeCallback();
              }
            }
            const innerData = parsedData.data;
            if (parsedData.event === EventEnum.WORKFLOW_STARTED) {
              const item: any = {
                type: 'workflow',
                content: result,
                files,
                agentThoughts,
                workflows: {
                  status: 'running',
                  nodes: [],
                },
              };
              contentBlocks.push(item);

              onUpdate({
                requestStatus: 'success',
                content: result,
                contentBlocks,
              });
            } else if (parsedData.event === EventEnum.WORKFLOW_FINISHED) {
              console.log('工作流结束', parsedData);
              lastItem.workflows.status = 'finished';

              onUpdate({
                content: result,
                contentBlocks,
              });
            } else if (parsedData.event === EventEnum.WORKFLOW_NODE_STARTED) {
              console.log('节点开始', parsedData);

              lastItem.workflows.nodes?.push({
                id: innerData.id,
                status: 'running',
                type: innerData.node_type,
                title: innerData.title,
                action_process: {
                  action_process: [] as any,
                  action_type: innerData.action_type,
                },
              } as IWorkflowNode);
              console.log('节点开始===contentBlocks', contentBlocks);

              onUpdate({
                requestStatus: 'success',
                content: result,
                contentBlocks,
              });
            } else if (parsedData.event === EventEnum.WORKFLOW_NODE_WORKING) {
              lastItem.workflows.nodes = lastItem.workflows.nodes?.map(
                (item: any, index: number) => {
                  if (index === lastItem.workflows.nodes.length - 1) {
                    console.log('节点进行中', item);

                    const curIndex = item.action_process.action_process.length - 1;
                    const curItem = item.action_process.action_process[curIndex];
                    if (curItem && curItem.id === innerData.action_process?.id) {
                      const content = innerData.action_process?.content;
                      const search_content = innerData.action_process?.search_content;
                      if (content) {
                        item.action_process.action_process[curIndex].content =
                          (item.action_process.action_process[curIndex]?.content || '') + content;
                      }
                      if (search_content) {
                        item.action_process.action_process[curIndex].search_content = [
                          ...(item.action_process.action_process[curIndex]?.search_content || []),
                          search_content,
                        ];
                      }
                      item.action_process.action_process[curIndex].status =
                        innerData.action_process?.status;
                    } else {
                      item.action_process.action_process.push(innerData.action_process);
                      item.action_process.action_type = innerData.action_type;
                    }

                    return {
                      ...item,
                      status: 'success',
                      inputs: innerData.inputs,
                      outputs: innerData.outputs,
                      process_data: innerData.process_data,
                      elapsed_time: innerData.elapsed_time,
                      execution_metadata: innerData.execution_metadata,
                    };
                  }
                  return item;
                },
              );

              onUpdate({
                requestStatus: 'success',
                content: result,
                contentBlocks,
              });
            } else if (parsedData.event === EventEnum.WORKFLOW_NODE_FINISHED) {
              lastItem.workflows.nodes = lastItem.workflows.nodes?.map(
                (item: any, index: number) => {
                  if (index === lastItem.workflows.nodes.length - 1) {
                    console.log('节点进行中', item);
                    return {
                      ...item,
                      status: 'success',
                      inputs: innerData.inputs,
                      outputs: innerData.outputs,
                      process_data: innerData.process_data,
                      elapsed_time: innerData.elapsed_time,
                      execution_metadata: innerData.execution_metadata,
                    };
                  }
                  return item;
                },
              );

              onUpdate({
                requestStatus: 'success',
                content: result,
                contentBlocks,
              });
            }
            if (parsedData.event === EventEnum.MESSAGE_FILE) {
              result += `<img src="${parsedData.url}" />`;
              const item: any = {
                type: 'file',
                content: result,
                files,
                agentThoughts,
                workflows,
              };
              contentBlocks.push(item);

              onUpdate({
                requestStatus: 'success',
                content: result,
                contentBlocks,
              });
            }
            if (parsedData.event === EventEnum.MESSAGE) {
              const text = parsedData.answer;
              console.log('reader=========message', parsedData);
              console.log('reader=========text', text);
              result += text;
              if (text) contentChunks.push(text);
              if (lastItem?.type === 'content') {
                lastItem.content += text;
                if (!lastItem.contentChunks) lastItem.contentChunks = [];
                if (text) lastItem.contentChunks.push(text);
              } else {
                const item: any = {
                  type: 'content',
                  content: text,
                  contentChunks: text ? [text] : [],
                  files,
                  agentThoughts,
                  workflows,
                };
                contentBlocks.push(item);
              }

              onUpdate({
                requestStatus: 'success',
                content: result,
                // @ts-expect-error contentChunks 类型扩展
                contentChunks: [...contentChunks],
                contentBlocks,
              });
            }
            if (parsedData.event === EventEnum.ERROR) {
              onError({
                name: `${(parsedData as unknown as IErrorEvent).status}: ${
                  (parsedData as unknown as IErrorEvent).code
                }`,
                message: (parsedData as unknown as IErrorEvent).message,
              });
              getConversationMessages(parsedData.conversation_id);
            }
            if (parsedData.event === EventEnum.AGENT_MESSAGE) {
              lastItem.type = 'agent_message';

              const lastAgentThought = lastItem.agentThoughts[lastItem.agentThoughts.length - 1];

              if (lastAgentThought) {
                // 将agent_message以流式形式输出到最后一条agent_thought里
                const text = parsedData.answer;
                lastAgentThought.thought += text;
              } else {
                continue;
              }

              onUpdate({
                requestStatus: 'success',
                content: result,
                contentBlocks,
              });
            }
            if (parsedData.event === EventEnum.AGENT_THOUGHT) {
              // const existAgentThoughtIndex = agentThoughts.findIndex(
              //   (_agentThought) => _agentThought.position === parsedData.position,
              // );

              const newAgentThought = {
                conversation_id: parsedData.conversation_id,
                id: parsedData.id as string,
                task_id: parsedData.task_id,
                position: parsedData.position,
                tool: parsedData.tool,
                tool_input: parsedData.tool_input,
                observation: parsedData.observation,
                message_files: parsedData.message_files,
                message_id: parsedData.message_id,
              } as IAgentThought;

              // if (existAgentThoughtIndex !== -1) {
              //   // 如果已存在一条，则替换内容
              //   agentThoughts[existAgentThoughtIndex] = newAgentThought;
              // } else {
              //   // 如果不存在，则插入一条
              //   agentThoughts.push(newAgentThought);
              // }

              if (lastItem?.type !== 'agent_thought') {
                const item: any = {
                  type: 'agent_thought',
                  content: result,
                  files,
                  agentThoughts,
                  workflows: {
                    status: 'running',
                    nodes: [],
                  },
                };
                contentBlocks.push(item);
              }
              lastItem.agentThoughts?.push(newAgentThought);

              onUpdate({
                requestStatus: 'success',
                content: result,
                contentBlocks,
              });
            }
          } else {
            console.log('没有数据', chunk);
            continue;
          }
        }
      } catch (error: any) {
        // 异常 => 结束
        console.error('error=====', error);
        const { response } = error;
        const errText = response?.statusText || '请求对话接口失败';
        // 打断输出
        abortRef.current = () => {
          // onError 是为了结束 agent 的 isRequesting 以更新 Sender 的发送按钮状态
          // onError({
          //   name: response?.status?.toString(),
          //   message: errText,
          // });
          onSuccess([
            {
              requestStatus: 'success',
              content: errText,
              message_id: currentMessageId,
              contentBlocks: [],
            },
          ]);
          abortController.abort?.();
        };
        abortRef.current();
      }
    },
  });

  const { onRequest, messages, setMessages } = useXChat({
    agent,
  });

  return { agent, onRequest, messages, setMessages, currentMessageId };
};
