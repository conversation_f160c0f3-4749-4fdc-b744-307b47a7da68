import { IActionProcessItem } from '@/types';
import {
  CheckCircleOutlined,
  InfoCircleOutlined,
  LinkOutlined,
  LoadingOutlined,
} from '@ant-design/icons';
import { ThoughtChainItem, ThoughtChain as XThoughtChain } from '@ant-design/x';
import { Button } from 'antd';
import { createStyles } from 'antd-style';
import { MarkdownRenderer } from '../markdown-renderer';
import animStyles from '../message/content.module.css';

interface IThoughtChainProps {
  /**
   * 类名
   */
  className?: string;
  /**
   * 思维链的唯一 Key
   */
  uniqueKey: string;
  /**
   * 原始思维链数据
   */
  items?: IActionProcessItem[];
  animate?: boolean;
}

function getStatusIcon(status: ThoughtChainItem['status']) {
  switch (status) {
    case 'success':
      return <CheckCircleOutlined />;
    case 'error':
      return <InfoCircleOutlined />;
    case 'pending':
      return <LoadingOutlined />;
    default:
      return undefined;
  }
}

const useStyle = createStyles(({ css }) => {
  return {
    layout: css`
      .ant-thought-chain-item-content::before {
        top: 0 !important;
      }
    `,
  };
});

/**
 * 思维链组件 (Ant Design X 思维链数据)
 */
export default function ThoughtChain(props: IThoughtChainProps) {
  const { uniqueKey, items, className, animate = true } = props;
  console.log('uniqueKey', uniqueKey);
  console.log('uniqueKey===items', items);
  const { styles } = useStyle();
  if (!items?.length) {
    return null;
  }

  const thoughtChainItems: ThoughtChainItem[] = items.map((item) => {
    return {
      title: <span className="text-base">{item.title || '暂无标题'}</span>,
      status: item.status,
      icon: getStatusIcon(item.status),
      content: item.content && <MarkdownRenderer markdownText={(item.content as string) || ''} />,
      description: (
        <>
          <p className={`text-wrap ${animate && animStyles['fade-in']}`}>{item.description}</p>
          {item.search_content &&
            item.search_content?.map((subItem, subIndex) => {
              return (
                <Button
                  className={`p-0 flex gap-x-1 ${animStyles['fade-in']}`}
                  key={subIndex}
                  type="link"
                  icon={<LinkOutlined />}
                  onClick={() => window.open(subItem.url, '_blank')}
                >
                  {subItem.title}
                </Button>
              );
            })}
          {/* <Collapse
            className="mt-3 min-w-chat-card"
            size="small"
            items={[
              {
                key: `${uniqueKey}-tool_input`,
                label: '输入',
                children: <CollapseItem text={item.description} />,
              },
              {
                key: `${uniqueKey}-observation`,
                label: '输出',
                children: <CollapseItem text={item.description} />,
              },
            ]}
          />
          <pre className="border-none">{item.description}</pre> */}
        </>
      ),
    };
  });

  if (!thoughtChainItems?.length) {
    return null;
  }

  return (
    <XThoughtChain
      className={`${styles.layout} ${className}`}
      classNames={{
        item: animStyles['fade-in'],
      }}
      styles={{
        itemContent: { marginLeft: '40px' },
      }}
      items={thoughtChainItems}
      collapsible={true}
    />
  );
}
