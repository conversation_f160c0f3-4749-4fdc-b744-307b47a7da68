# 会话ID更新测试指南

## 测试目标

验证聊天结束后临时会话ID更新为真实ID时，聊天内容不会丢失。

## 测试环境

- 浏览器：Chrome/Firefox/Safari
- 开发者工具：打开Console标签页
- 项目地址：http://localhost:8001

## 测试步骤

### 1. 准备测试环境

1. 打开浏览器开发者工具 (F12)
2. 切换到 Console 标签页
3. 访问 http://localhost:8001
4. 确保页面正常加载

### 2. 测试临时ID更新场景

#### 步骤A：创建新对话
1. 点击 "新增对话" 按钮
2. 观察Console日志，应该看到类似：
   ```
   会话ID变化检测: {
     prev: undefined,
     current: "temp_xxxxx",
     isTempIdUpdate: false,
     shouldReload: true
   }
   🔄 真正的会话切换，重新加载内容
   ```

#### 步骤B：发送第一条消息
1. 在输入框中输入测试消息，例如："你好，这是测试消息"
2. 点击发送按钮
3. 等待AI开始回复

#### 步骤C：观察ID更新过程
1. 等待AI回复完成
2. **关键观察点**：在AI回复完成后，观察Console日志
3. 应该看到类似以下日志：
   ```
   会话ID变化检测: {
     prev: "temp_xxxxx",
     current: "123456",
     isTempIdUpdate: true,
     shouldReload: false
   }
   🔄 临时ID更新为真实ID，保持当前聊天内容
   ```

#### 步骤D：验证聊天内容保持
1. **检查聊天内容**：确认之前的对话内容仍然显示
2. **检查对话列表**：确认左侧对话列表已更新
3. **测试继续聊天**：发送另一条消息，确认可以正常聊天

### 3. 测试真正的会话切换

#### 步骤A：切换到其他会话
1. 在左侧对话列表中点击其他会话
2. 观察Console日志，应该看到：
   ```
   会话ID变化检测: {
     prev: "123456",
     current: "789012",
     isTempIdUpdate: false,
     shouldReload: true
   }
   🔄 真正的会话切换，重新加载内容
   ```

#### 步骤B：验证内容重新加载
1. 确认聊天内容被清空并重新加载
2. 确认显示的是新会话的历史消息

### 4. 边界情况测试

#### 测试场景1：快速切换会话
1. 在AI回复过程中快速切换会话
2. 确认不会出现异常

#### 测试场景2：网络错误
1. 断开网络连接
2. 发送消息触发错误
3. 确认错误处理正常

## 预期结果

### ✅ 成功标准

1. **临时ID更新时**：
   - 聊天内容保持不变
   - 对话列表正确更新
   - Console显示正确的日志
   - 可以继续聊天

2. **真正会话切换时**：
   - 聊天内容正确清空并重新加载
   - 显示正确的历史消息
   - Console显示正确的日志

3. **整体体验**：
   - 无异常错误
   - 界面响应流畅
   - 用户操作符合预期

### ❌ 失败标准

1. **临时ID更新时聊天内容丢失**
2. **会话切换时内容没有正确加载**
3. **Console出现错误日志**
4. **界面卡死或异常**

## 故障排除

### 问题1：看不到Console日志
- 确认开发者工具已打开
- 确认在Console标签页
- 刷新页面重新测试

### 问题2：临时ID更新时内容仍然丢失
- 检查 `isTempId()` 函数是否正常工作
- 检查 `prevConversationIdRef` 是否正确更新
- 查看是否有JavaScript错误

### 问题3：会话切换异常
- 检查网络连接
- 查看API请求是否正常
- 检查服务器响应

## 测试报告模板

```
测试时间：____年__月__日
测试人员：________
浏览器版本：________

测试结果：
□ 临时ID更新 - 内容保持 ✅/❌
□ 真正会话切换 - 内容重新加载 ✅/❌
□ Console日志正确 ✅/❌
□ 无异常错误 ✅/❌

问题记录：
1. ________________
2. ________________

总体评价：✅ 通过 / ❌ 失败
```

## 回归测试

在每次相关代码修改后，都应该运行此测试以确保功能正常。
