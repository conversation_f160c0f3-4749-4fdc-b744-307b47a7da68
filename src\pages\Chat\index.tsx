import { XProvider } from '@ant-design/x';
import { createStyles } from 'antd-style';
import React, { useCallback, useRef, useState } from 'react';

import '@/App.css';
import { DEFAULT_CONVERSATION_NAME } from '@/constants';
import chat from '@/services/chat';
import { ChatAPI } from '@/services/chat/typeings';
import { isTempId } from '@/utils';
import { PlusOutlined, SettingOutlined, UserOutlined } from '@ant-design/icons';
import { Conversation } from '@ant-design/x/es/conversations';
import { useModel } from '@umijs/max';
import { useMount } from 'ahooks';
import { Avatar, Button, Empty, message, Spin } from 'antd';
import ChatboxWrapper from './components/ChatboxWrapper';
import { ConversationList } from './components/ConversationList';
import { Logo } from './components/Logo';
import { IGetAppParametersResponse } from './type';

const useStyle = createStyles(({ token, css }) => {
  return {
    layout: css`
      width: 100%;
      min-width: 1000px;
      height: 100vh;
      border-radius: ${token.borderRadius}px;
      display: flex;
      background: ${token.colorBgContainer};
      font-family: AlibabaPuHuiTi, ${token.fontFamily}, sans-serif;

      .ant-prompts {
        color: ${token.colorText};
      }
    `,
    menu: css`
      background: ${token.colorBgLayout}80;
      width: 280px;
      height: 100%;
      display: flex;
      flex-direction: column;
      padding: 0 12px;
    `,
    conversations: css`
      padding: 0 12px;
      flex: 1;
      overflow-y: auto;
    `,
    chat: css`
      height: 100%;
      width: calc(100% - 280px);
      margin: 0 auto;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      padding: ${token.paddingLG}px 0;
      gap: 16px;
    `,
    messages: css`
      flex: 1;
    `,
    placeholder: css`
      padding-top: 32px;
    `,
    sender: css`
      box-shadow: ${token.boxShadow};
    `,
    addBtn: css`
      background: #1677ff0f;
      border: 1px solid #1677ff34;
      width: 100%;
      margin-bottom: 12px;
    `,
    siderFooter: css`
      border-top: 1px solid ${token.colorBorderSecondary};
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    `,
  };
});

const Independent: React.FC = () => {
  const { styles } = useStyle();

  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};

  const [conversationsItems, setConversationsItems] = useState<Conversation[]>([]);
  const [conversationListLoading, setCoversationListLoading] = useState<boolean>(false);
  const [currentConversationId, setCurrentConversationId] = useState<string>();

  // const [attachedFiles, setAttachedFiles] = useState<GetProp<typeof Attachments, 'items'>>([]);

  const [appParameters, setAppParameters] = useState<IGetAppParametersResponse>();

  // 防抖刷新对话列表的引用
  const debouncedRefreshRef = useRef<any>(null);

  // 创建防抖版本的刷新函数
  const debouncedGetConversationItems = useCallback(
    (options?: { force?: boolean; newConversationId?: string; skipLoading?: boolean }) => {
      // 如果已经有防抖函数，先清除
      if (debouncedRefreshRef.current) {
        debouncedRefreshRef.current.cancel();
      }

      // 创建新的防抖函数
      const { debounce } = require('lodash');
      debouncedRefreshRef.current = debounce(() => {
        getConversationItems(options);
      }, 300); // 300ms 防抖延迟

      // 执行防抖函数
      debouncedRefreshRef.current();
    },
    [],
  );

  // ==================== Runtime ====================
  const init = async () => {
    // 获取信息
    const res = await chat.api.getTips();
    console.log('init', res);

    setAppParameters({
      openingStatement: '欢迎使用 OIG Agent Chat',
      openingDesc: '基于 OIG API，OIG Chat是一个可以与人工智能交互的网络应用程序。',
      promptsItems: res,
    });
  };

  const onAddConversation = async () => {
    // 创建新对话
    // const res = await chat.api.createSession({
    //   user_id: currentUser?.user_id,
    // });
    const newKey = `temp_${Math.random()}`;
    setConversationsItems((prev) => {
      return [
        {
          key: newKey,
          label: DEFAULT_CONVERSATION_NAME,
          group: '今天',
        },
        ...prev,
      ];
    });
    setCurrentConversationId(newKey);
  };

  /**
   * 获取对话列表
   * @param options 刷新选项
   */
  const getConversationItems = async (options?: {
    force?: boolean;
    newConversationId?: string;
    skipLoading?: boolean; // 跳过loading状态，用于增量更新
  }) => {
    const { force = false, newConversationId, skipLoading = false } = options || {};

    // 智能刷新逻辑：只在以下情况刷新
    // 1. 强制刷新 (force = true)
    // 2. 有新对话ID需要添加到列表
    // 3. 当前对话列表为空
    if (!force && !newConversationId && conversationsItems.length > 0) {
      console.log('🚫 跳过对话列表刷新：无需刷新条件', {
        force,
        newConversationId,
        listLength: conversationsItems.length,
      });
      return;
    }

    console.log('🔄 开始刷新对话列表', {
      force,
      newConversationId,
      skipLoading,
      listLength: conversationsItems.length,
    });

    if (!skipLoading) {
      setCoversationListLoading(true);
    }

    try {
      const result = await chat.api.getUserSession({
        user_id: currentUser?.user_id,
      });
      console.log('✅ 获取对话列表成功:', result);

      const newItems: Conversation[] = result.sessions.map((item: ChatAPI.GetSessionInfoRes) => {
        return {
          ...item,
          label: item.session_name,
          key: String(item.session_id),
        };
      });

      if (!newItems?.length) {
        setConversationsItems([]);
        onAddConversation();
      } else {
        setConversationsItems(newItems);

        // 如果有新对话ID，则切换到新对话
        if (newConversationId) {
          setCurrentConversationId(newConversationId);
        } else if (isTempId(currentConversationId) || !currentConversationId) {
          setCurrentConversationId(newItems[0]?.key);
        }
      }
    } catch (error) {
      console.error(error);
      message.error(`获取会话列表失败: ${error}`);
    } finally {
      if (!skipLoading) {
        setCoversationListLoading(false);
      }
    }
  };

  // 初始化获取应用列表
  useMount(() => {
    init();
    // 初始化时强制刷新对话列表
    getConversationItems({ force: true });
  });

  // ==================== Event ====================

  // 重命名会话
  const renameConversationPromise = async (conversationId: string, name: string) => {
    console.log('renameConversation', conversationId, name);
    await chat.api.renameSession({ session_id: conversationId, name });
    setConversationsItems((items) => {
      const list = items.map((item) => {
        if (item.key === conversationId) {
          return {
            ...item,
            label: name,
          };
        }
        return item;
      });
      console.log('list', list);

      return list;
    });
    console.log('conversationsItems', conversationsItems);
  };

  // 删除会话
  const deleteConversationPromise = async (conversationId: string) => {
    console.log('deleteConversation', conversationId);
    await chat.api.deleteSession({ session_id: conversationId });
  };

  return (
    <XProvider>
      <div className={styles.layout}>
        <div className={styles.menu}>
          {/* Logo */}
          <Logo />
          {/* 添加会话 */}
          <Button
            onClick={onAddConversation}
            type="link"
            className={styles.addBtn}
            icon={<PlusOutlined />}
          >
            新增对话
          </Button>
          {/* 会话管理 */}
          <div className="flex-1 overflow-y-auto">
            <Spin spinning={conversationListLoading}>
              {conversationsItems?.length ? (
                <ConversationList
                  renameConversationPromise={(conversationId: string, name: string) =>
                    renameConversationPromise(conversationId, name)
                  }
                  deleteConversationPromise={deleteConversationPromise}
                  items={conversationsItems}
                  activeKey={currentConversationId}
                  onActiveChange={(id) => setCurrentConversationId(id)}
                  onItemsChange={setConversationsItems}
                  refreshItems={() => getConversationItems({ force: true })}
                />
              ) : (
                <Empty className="pt-6" description="暂无会话" />
              )}
            </Spin>
          </div>
          <div className={styles.siderFooter}>
            <div className="flex items-center gap-2">
              <Avatar style={{ backgroundColor: '#87d068' }} icon={<UserOutlined />} size={24} />
              <div>{currentUser?.username}</div>
            </div>
            <Button type="text" icon={<SettingOutlined />} />
          </div>
        </div>

        {/* 右侧聊天窗口 */}
        <div className={styles.chat}>
          {/* 新增外层容器 */}
          {conversationListLoading ? (
            <div className="w-full flex-1 flex items-center justify-center">
              <Spin spinning />
            </div>
          ) : (
            <ChatboxWrapper
              conversationId={currentConversationId}
              conversationItems={conversationsItems}
              onConversationIdChange={setCurrentConversationId}
              appParameters={appParameters}
              onItemsChange={setConversationsItems}
              conversationItemsChangeCallback={debouncedGetConversationItems}
            />
          )}
        </div>
      </div>
    </XProvider>
  );
};

export default Independent;
